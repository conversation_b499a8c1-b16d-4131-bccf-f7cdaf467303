# 腾讯云部署指南 - 肺功能数据管理平台

## 前提条件

- 腾讯云CVM (2核4GB Ubuntu 20.04 或更高版本)
- 安全组开放3000端口和80端口
- 腾讯云轻量数据库已配置 (MySQL)
- 确保已有GitHub账号可访问项目代码

## 部署方式选择

本项目支持两种部署方式：

### 🐳 Docker部署（推荐用于快速部署）
- **优势**：环境隔离、一键部署、易于管理
- **适用场景**：快速部署、测试环境、容器化环境

### 🚀 传统部署（推荐用于生产环境）
- **优势**：性能更好、资源占用少、调试方便、配置灵活
- **适用场景**：生产环境、性能要求高、需要精细控制

---

## 方式一：传统部署（非Docker）

### 🚀 一键部署脚本（推荐）

```bash
# 下载并执行一键部署脚本
curl -fsSL https://raw.githubusercontent.com/peckbyte/free_lung_function_project_admin/main/scripts/deploy-traditional.sh | bash

# 或者手动下载执行
wget https://raw.githubusercontent.com/peckbyte/free_lung_function_project_admin/main/scripts/deploy-traditional.sh
chmod +x deploy-traditional.sh
./deploy-traditional.sh
```

**脚本功能**：
- ✅ 自动检查系统要求
- ✅ 安装Node.js 18+、Yarn、PM2、Nginx
- ✅ 克隆项目代码并安装依赖
- ✅ 配置PM2进程管理
- ✅ 配置Nginx反向代理
- ✅ 设置开机自启动

**部署后需要手动配置**：
1. 编辑环境变量：`nano ~/apps/free_lung_function_project_admin/.env.production`
2. 运行数据库迁移：`cd ~/apps/free_lung_function_project_admin && npx prisma db push`
3. 创建管理员用户：`cd ~/apps/free_lung_function_project_admin && node reset-password.js`

### 📋 手动部署步骤（详细版）

### 系统要求

- **CPU**: 2核心以上
- **内存**: 4GB RAM以上
- **磁盘**: 20GB 可用空间
- **操作系统**: Ubuntu 20.04+ / CentOS 7+

### 1. 系统环境准备

#### 1.1 更新系统包

```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

#### 1.2 安装Node.js 18+

```bash
# 使用NodeSource仓库安装最新LTS版本
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version  # 应该显示 v18.x.x 或更高
npm --version
```

#### 1.3 安装Yarn包管理器

```bash
# 安装Yarn
npm install -g yarn

# 验证安装
yarn --version
```

#### 1.4 安装PM2进程管理器

```bash
# 全局安装PM2
npm install -g pm2

# 验证安装
pm2 --version
```

#### 1.5 安装Nginx

```bash
# Ubuntu/Debian
sudo apt install nginx -y

# 启动并设置开机自启
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查状态
sudo systemctl status nginx
```

### 2. 应用部署

#### 2.1 创建应用用户和目录

```bash
# 创建应用用户（推荐使用非root用户）
sudo useradd -m -s /bin/bash lungapp
sudo usermod -aG sudo lungapp

# 切换到应用用户
sudo su - lungapp

# 创建应用目录
mkdir -p /home/<USER>/apps
cd /home/<USER>/apps
```

#### 2.2 克隆项目代码

```bash
# 克隆项目
git clone https://github.com/peckbyte/free_lung_function_project_admin.git
cd free_lung_function_project_admin

# 设置目录权限
chmod -R 755 .
```

#### 2.3 安装项目依赖

```bash
# 安装依赖（使用Yarn）
yarn install --production=false

# 如果网络较慢，可以使用国内镜像
yarn config set registry https://registry.npmmirror.com
yarn install --production=false
```

### 3. 环境配置

#### 3.1 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env.production

# 编辑生产环境配置
nano .env.production
```

**生产环境配置示例**：

```bash
# 应用配置
NODE_ENV=production
PORT=3000

# 数据库配置（使用腾讯云内网地址获得更好性能）
DATABASE_URL="mysql://srmyy_123:gg9gpaEqkg4s@*********:3306/srmyy_123"

# NextAuth 配置
NEXTAUTH_SECRET="your-very-secure-nextauth-secret-change-this-in-production"
NEXTAUTH_URL="http://your-server-ip:3000"

# JWT 密钥
JWT_SECRET="your-very-secure-jwt-secret-change-this-in-production"

# 日志配置
LOG_LEVEL="info"
LOG_FILE="/home/<USER>/apps/free_lung_function_project_admin/logs/production.log"

# 文件上传配置
UPLOAD_MAX_SIZE="10485760"  # 10MB
UPLOAD_ALLOWED_TYPES="xlsx,csv,pdf"

# 应用信息
APP_VERSION="1.0.0"
APP_NAME="肺功能数据管理平台"
```

#### 3.2 创建必要目录

```bash
# 创建日志和上传目录
mkdir -p logs public/uploads

# 设置权限
chmod 755 logs public/uploads
```

### 4. 数据库初始化

#### 4.1 生成Prisma客户端

```bash
# 生成Prisma客户端
npx prisma generate
```

#### 4.2 运行数据库迁移

```bash
# 推送数据库架构
npx prisma db push

# 验证数据库连接
npx prisma db pull
```

#### 4.3 创建管理员用户（可选）

```bash
# 运行用户创建脚本
node reset-password.js
```

### 5. 构建生产版本

```bash
# 构建Next.js应用
yarn build

# 验证构建结果
ls -la .next/
```

### 6. PM2进程管理配置

#### 6.1 创建PM2配置文件

```bash
# 创建PM2配置文件
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'lung-function-admin',
    script: 'yarn',
    args: 'start',
    cwd: '/home/<USER>/apps/free_lung_function_project_admin',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_file: '.env.production',
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000
  }]
}
EOF
```

#### 6.2 启动应用

```bash
# 使用PM2启动应用
pm2 start ecosystem.config.js

# 查看应用状态
pm2 status

# 查看应用日志
pm2 logs lung-function-admin

# 查看实时日志
pm2 logs lung-function-admin --lines 50
```

#### 6.3 配置PM2自启动

```bash
# 保存当前PM2进程列表
pm2 save

# 生成开机自启动脚本
pm2 startup

# 按照提示执行生成的命令（通常需要sudo权限）
# 例如：sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u lungapp --hp /home/<USER>
```

### 7. Nginx反向代理配置

#### 7.1 创建Nginx配置文件

```bash
# 切换到root用户配置Nginx
sudo su -

# 创建站点配置文件
cat > /etc/nginx/sites-available/lung-function-admin << 'EOF'
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;  # 替换为你的域名或IP

    # 安全配置
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # 日志配置
    access_log /var/log/nginx/lung-function-admin.access.log;
    error_log /var/log/nginx/lung-function-admin.error.log;

    # 静态文件直接服务
    location /_next/static/ {
        alias /home/<USER>/apps/free_lung_function_project_admin/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    location /uploads/ {
        alias /home/<USER>/apps/free_lung_function_project_admin/public/uploads/;
        expires 30d;
        add_header Cache-Control "public";
        access_log off;
    }

    # 主应用反向代理
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;

        # 缓存配置
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    # 健康检查端点
    location /api/health {
        proxy_pass http://127.0.0.1:3000/api/health;
        access_log off;
    }

    # 文件上传大小限制
    client_max_body_size 10M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
EOF
```

#### 7.2 启用站点配置

```bash
# 创建软链接启用站点
ln -s /etc/nginx/sites-available/lung-function-admin /etc/nginx/sites-enabled/

# 删除默认站点（可选）
rm -f /etc/nginx/sites-enabled/default

# 测试Nginx配置
nginx -t

# 重新加载Nginx配置
systemctl reload nginx

# 检查Nginx状态
systemctl status nginx
```

### 8. 防火墙配置

```bash
# 配置UFW防火墙
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# 检查防火墙状态
sudo ufw status
```

### 9. 验证部署

#### 9.1 检查服务状态

```bash
# 检查PM2进程
pm2 status

# 检查Nginx状态
sudo systemctl status nginx

# 检查应用日志
pm2 logs lung-function-admin --lines 20
```

#### 9.2 健康检查

```bash
# 检查应用是否正常响应
curl http://localhost:3000/api/health

# 检查通过Nginx的访问
curl http://localhost/api/health

# 预期返回
{"status":"ok","database":"connected","timestamp":"2024-01-01T00:00:00.000Z"}
```

#### 9.3 访问应用

打开浏览器访问: `http://your-server-ip`

**默认登录信息**：
- 使用密码重置脚本创建管理员账户：`node reset-password.js`
- 默认管理员用户名：admin

### 10. SSL配置（可选，推荐生产环境）

#### 10.1 安装Certbot

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y
```

#### 10.2 获取SSL证书

```bash
# 获取Let's Encrypt免费SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 按照提示完成配置
# 选择重定向HTTP到HTTPS（推荐选择2）
```

#### 10.3 配置自动续期

```bash
# 测试自动续期
sudo certbot renew --dry-run

# 添加自动续期任务到crontab
sudo crontab -e

# 添加以下行（每月1日凌晨2点执行）
0 2 1 * * /usr/bin/certbot renew --quiet && /usr/bin/systemctl reload nginx
```

### 11. 监控和维护

#### 11.1 日志管理

```bash
# 配置日志轮转
sudo cat > /etc/logrotate.d/lung-function-admin << 'EOF'
/home/<USER>/apps/free_lung_function_project_admin/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 lungapp lungapp
    postrotate
        pm2 reload lung-function-admin
    endscript
}
EOF

# 配置Nginx日志轮转
sudo cat > /etc/logrotate.d/nginx-lung-function << 'EOF'
/var/log/nginx/lung-function-admin.*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
EOF
```

#### 11.2 监控脚本

```bash
# 创建健康检查脚本
cat > /home/<USER>/health-check.sh << 'EOF'
#!/bin/bash

# 健康检查脚本
APP_URL="http://localhost:3000/api/health"
LOG_FILE="/home/<USER>/health-check.log"

# 检查应用健康状态
response=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL)

if [ $response -eq 200 ]; then
    echo "$(date): Application is healthy" >> $LOG_FILE
else
    echo "$(date): Application health check failed (HTTP $response)" >> $LOG_FILE
    # 重启应用
    pm2 restart lung-function-admin
    echo "$(date): Application restarted" >> $LOG_FILE
fi
EOF

chmod +x /home/<USER>/health-check.sh

# 添加到crontab（每5分钟检查一次）
crontab -e
# 添加以下行
*/5 * * * * /home/<USER>/health-check.sh
```

#### 11.3 性能监控

```bash
# 安装htop用于系统监控
sudo apt install htop -y

# 查看系统资源使用情况
htop

# 查看PM2监控信息
pm2 monit

# 查看应用内存使用
pm2 show lung-function-admin
```

### 12. 常见问题排查

#### 12.1 应用无法启动

```bash
# 检查PM2日志
pm2 logs lung-function-admin

# 检查环境变量
pm2 show lung-function-admin

# 手动启动测试
cd /home/<USER>/apps/free_lung_function_project_admin
NODE_ENV=production yarn start
```

#### 12.2 数据库连接问题

```bash
# 测试数据库连接
cd /home/<USER>/apps/free_lung_function_project_admin
npx prisma db pull

# 检查数据库URL配置
grep DATABASE_URL .env.production

# 测试网络连接
telnet ********* 3306
```

#### 12.3 Nginx配置问题

```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 查看站点访问日志
sudo tail -f /var/log/nginx/lung-function-admin.access.log
```

#### 12.4 权限问题

```bash
# 修复文件权限
sudo chown -R lungapp:lungapp /home/<USER>/apps/free_lung_function_project_admin
chmod -R 755 /home/<USER>/apps/free_lung_function_project_admin
chmod -R 777 /home/<USER>/apps/free_lung_function_project_admin/logs
chmod -R 777 /home/<USER>/apps/free_lung_function_project_admin/public/uploads
```

### 13. 应用更新

#### 13.1 更新流程

```bash
# 切换到应用目录
cd /home/<USER>/apps/free_lung_function_project_admin

# 备份当前版本
git tag backup-$(date +%Y%m%d-%H%M%S)

# 拉取最新代码
git pull origin main

# 安装新依赖
yarn install

# 运行数据库迁移
npx prisma generate
npx prisma db push

# 重新构建
yarn build

# 重启应用
pm2 restart lung-function-admin

# 检查状态
pm2 status
pm2 logs lung-function-admin --lines 20
```

#### 13.2 回滚操作

```bash
# 如果更新出现问题，可以回滚到之前的版本
git reset --hard backup-YYYYMMDD-HHMMSS
yarn install
yarn build
pm2 restart lung-function-admin
```

### 14. 性能优化建议

#### 14.1 系统级优化

```bash
# 调整系统文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65536" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### 14.2 应用级优化

```bash
# 启用PM2集群模式（多核CPU）
# 修改ecosystem.config.js中的instances配置
instances: 'max',  # 或者指定具体数量，如 2
exec_mode: 'cluster'
```

#### 14.3 数据库优化

```bash
# 在腾讯云控制台中：
# 1. 启用慢查询日志
# 2. 配置适当的连接池大小
# 3. 启用查询缓存
# 4. 定期优化表结构
```

---

## 方式二：Docker部署

### 一键部署（推荐）

```bash
curl -fsSL https://raw.githubusercontent.com/your-repo/free_lung_function_project_admin/main/scripts/quick_deploy.sh | bash
```

### 手动部署步骤

### 1. 拉取git代码

```bash
git clone https://github.com/your-username/free_lung_function_project_admin.git
```

### 2. 文件夹授权

```bash
chmod -R 777 free_lung_function_project_admin
```

### 3. 腾讯云安装位置

```bash
cd /www/wwwroot/free_lung_function_project_admin
```

### 4. 配置腾讯云Docker镜像加速（官方配置）

```bash
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com"
  ]
}
EOF
```

### 5. 安装Docker和依赖

```bash
sudo apt update && sudo apt install -y docker.io docker-compose git curl
sudo systemctl restart docker
sudo systemctl enable docker
```

### 6. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（重要！）
nano .env
```

**关键环境变量配置**：

```bash
# 生产环境数据库 (使用内网地址获得更好性能)
DATABASE_URL="mysql://srmyy_123:gg9gpaEqkg4s@*********:3306/srmyy_123"

# NextAuth 配置
NEXTAUTH_SECRET="your-secure-nextauth-secret-change-this"
NEXTAUTH_URL="http://your-server-ip:3000"

# JWT 密钥
JWT_SECRET="your-secure-jwt-secret-change-this"

# 应用配置
NODE_ENV="production"
PORT="3000"
```

### 7. 快速启动

#### 方式一：本地运行（推荐用于开发）

```bash
# 安装依赖
yarn install

# 生成Prisma客户端
npx prisma generate

# 启动开发服务器
yarn dev
```

#### 方式二：Docker容器运行（推荐用于生产）

```bash
# 停止现有容器（如果有的话）
docker-compose -f docker-compose.simple.yml down

# 使用简化版Docker配置（推荐）
docker-compose -f docker-compose.simple.yml up -d --build

# 或使用开发版Docker配置
docker-compose -f docker-compose.dev.yml up -d --build
```

#### 方式三：传统Docker配置

```bash
# 使用完整的docker-compose配置（包含MySQL和Redis）
docker-compose up -d --build
```

## 🚀 性能优化版本

### 超快版（网络很慢时推荐）

创建 `docker-compose.fast.yml`：

```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.fast
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./public/uploads:/app/public/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

创建 `Dockerfile.fast`：

```dockerfile
# 使用腾讯云加速的Node.js镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 使用腾讯云NPM镜像加速
RUN yarn config set registry https://mirrors.tencent.com/npm/

# 复制package文件
COPY package*.json ./
COPY yarn.lock* ./

# 安装依赖
RUN yarn install --production --frozen-lockfile

# 复制应用代码
COPY . .

# 生成Prisma客户端
RUN npx prisma generate

# 构建应用
RUN yarn build

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["yarn", "start"]
```

启动超快版：

```bash
docker-compose -f docker-compose.fast.yml up -d --build
```

## 🔄 服务器端重新部署教程

### ⚠️ 常见问题：Git冲突解决

如果遇到 `error: Your local changes would be overwritten by merge` 错误：

```bash
# 推荐解决方案：强制更新到最新版本
cd /www/wwwroot/free_lung_function_project_admin
git fetch origin main
git reset --hard origin/main
```

### 1. 拉取最新代码

```bash
git pull

# 如果出现文件冲突：强制覆盖本地修改（推荐）
git fetch origin main
git reset --hard origin/main
```

### 2. 重新构建并启动

```bash
# 停止现有容器
docker-compose -f docker-compose.simple.yml down

# 重新构建并启动（推荐）
docker-compose -f docker-compose.simple.yml up -d --build

# 或使用快速版本
docker-compose -f docker-compose.fast.yml up -d --build

# 传统版本（不推荐，包含MySQL）
docker-compose down
docker-compose up -d --build
```

## 🐛 登录问题调试和修复

### 问题：密码重置后仍无法登录

**症状**：开发环境正常登录，Docker环境无法登录显示"用户名或密码错误"

**解决方案**：

#### 1. 密码重置（推荐使用Node.js版本）

```bash
# 使用Node.js版本的密码重置脚本（与系统完全兼容）
node reset-password.js

# 或使用Python版本（可能存在bcrypt兼容性问题）
python reset_password.py
```

#### 2. Docker环境调试

```bash
# 查看容器日志（包含详细的认证调试信息）
docker-compose -f docker-compose.simple.yml logs -f app

# 进入容器运行调试脚本
docker-compose -f docker-compose.simple.yml exec app node docker-debug.js

# 在容器内测试密码验证
docker-compose -f docker-compose.simple.yml exec app node reset-password.js
```

#### 3. 环境变量验证

```bash
# 检查容器内的环境变量
docker-compose -f docker-compose.simple.yml exec app printenv | grep -E "(DATABASE_URL|NEXTAUTH|JWT)"

# 验证数据库连接
docker-compose -f docker-compose.simple.yml exec app npx prisma db push
```

#### 4. 常见修复步骤

```bash
# 完整的问题排查和修复流程：

# 1. 停止并重建容器（确保最新配置生效）
docker-compose -f docker-compose.simple.yml down
docker-compose -f docker-compose.simple.yml up -d --build

# 2. 在容器内重置密码
docker-compose -f docker-compose.simple.yml exec app node reset-password.js

# 3. 查看认证日志
docker-compose -f docker-compose.simple.yml logs -f app

# 4. 测试登录（浏览器访问 http://your-server-ip:3011）
```

#### 5. 调试脚本说明

项目包含以下调试工具：

- `reset-password.js` - Node.js版本的密码重置脚本（推荐）
- `reset_password.py` - Python版本的密码重置脚本
- `docker-debug.js` - 容器内环境调试脚本

**注意**：这些脚本已加入`.gitignore`，不会提交到版本控制

## 数据库初始化

### 自动数据库初始化

```bash
# 进入应用容器
docker exec -it free_lung_function_project_admin_app_1 /bin/sh

# 运行数据库迁移
npx prisma migrate deploy

# 生成Prisma客户端
npx prisma generate

# 运行数据库种子（可选）
npm run db:seed
```

### 验证数据库连接

```bash
# 检查数据库连接
docker exec -it free_lung_function_project_admin_app_1 npm run db:studio

# 或者运行健康检查
curl http://localhost:3000/api/health
```

## 常见错误排查

### 🚨 修复数据库连接错误

**问题**：无法连接到腾讯云数据库

**解决方案**：

```bash
cd /www/wwwroot/free_lung_function_project_admin

# 1. 检查环境变量配置
cat .env | grep DATABASE_URL

# 2. 确保使用正确的数据库URL
# 生产环境使用内网地址
echo 'DATABASE_URL="mysql://srmyy_123:gg9gpaEqkg4s@*********:3306/srmyy_123"' >> .env

# 3. 重启容器
docker-compose restart app

# 4. 验证连接
docker exec -it free_lung_function_project_admin_app_1 npx prisma db push
```

### 🔧 修复端口占用问题

**问题**：端口3000已被占用

**解决方案**：

```bash
# 查找占用端口的进程
sudo lsof -i :3000

# 停止占用进程
sudo kill -9 [PID]

# 或者使用不同端口
echo 'PORT=3001' >> .env
docker-compose up -d --build
```

### 🛠️ 修复权限问题

**问题**：文件权限错误

**解决方案**：

```bash
# 修复文件权限
sudo chown -R $USER:$USER /www/wwwroot/free_lung_function_project_admin
chmod -R 755 /www/wwwroot/free_lung_function_project_admin

# 确保日志和上传目录可写
mkdir -p logs public/uploads
chmod -R 777 logs public/uploads
```

## 常用管理命令

```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f app

# 重启服务
docker-compose restart app

# 进入容器调试
docker exec -it free_lung_function_project_admin_app_1 /bin/sh

# 强制重建镜像
docker-compose build --no-cache app

# 清理Docker缓存
docker system prune -a
```

## 验证部署成功

### 1. 健康检查

```bash
# 检查应用是否正常运行
curl http://localhost:3000/api/health

# 预期返回
{"status":"ok","database":"connected","timestamp":"2024-01-01T00:00:00.000Z"}
```

### 2. 访问系统

打开浏览器访问: `http://YOUR_SERVER_IP:3011`

**默认登录信息**：

- 使用密码重置脚本创建管理员账户：`node reset-password.js`
- 或通过数据库种子创建：`npx prisma db seed`
- 默认管理员用户名：admin

### 3. 数据库验证

```bash
# 检查数据库表是否创建成功
docker exec -it free_lung_function_project_admin_app_1 npx prisma db push
```

## 生产环境优化

### 启用Nginx反向代理（推荐）

```bash
# 使用生产环境配置
docker-compose --profile production up -d
```

### 配置域名和SSL

1. 将域名解析指向服务器IP
2. 配置 `docker/nginx/nginx.conf`
3. 申请SSL证书并放置在 `docker/nginx/ssl/` 目录

### 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
df -h

# 查看内存使用情况
free -h
```

## 数据备份与恢复

### 备份数据

```bash
# 备份腾讯云数据库（推荐使用腾讯云控制台）
# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz public/uploads

# 备份日志
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs
```

### 恢复数据

```bash
# 恢复上传文件
tar -xzf uploads_backup_20240101.tar.gz

# 重启服务
docker-compose restart app
```

## 更新和维护

### 应用更新

```bash
# 1. 备份当前版本
git tag backup-$(date +%Y%m%d)

# 2. 拉取最新代码
git pull origin main

# 3. 重新部署（推荐使用simple版本）
docker-compose -f docker-compose.simple.yml down
docker-compose -f docker-compose.simple.yml up -d --build

# 4. 运行数据库迁移
docker-compose -f docker-compose.simple.yml exec app npx prisma migrate deploy
```

### 定期维护

```bash
# 清理Docker镜像和容器
docker system prune -f

# 查看日志大小并轮转
du -sh logs/

# 重启服务（定期维护）
docker-compose -f docker-compose.simple.yml restart app

# 查看服务状态
docker-compose -f docker-compose.simple.yml ps
```

## 安全配置

### 1. 防火墙设置

```bash
# 只开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 3011  # 应用端口 (docker-compose.simple.yml默认)
sudo ufw allow 80    # HTTP (如果使用Nginx)
sudo ufw allow 443   # HTTPS (如果使用Nginx)
sudo ufw enable
```

### 2. 密钥管理

- 更改 `.env` 文件中的默认密钥
- 使用强密码生成器生成安全的密钥
- 定期轮换密钥

### 3. 数据库安全

- 使用腾讯云数据库的内网地址
- 配置数据库访问白名单
- 启用数据库审计日志

## 快速部署脚本（传统部署）

为了简化传统部署流程，建议创建以下自动化脚本：

### 创建一键部署脚本

```bash
# 创建部署脚本
cat > /home/<USER>/deploy-traditional.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 开始传统方式部署肺功能数据管理平台..."

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo "❌ 请不要使用root用户运行此脚本"
    exit 1
fi

# 项目目录
PROJECT_DIR="/home/<USER>/apps/free_lung_function_project_admin"

# 检查项目目录是否存在
if [ ! -d "$PROJECT_DIR" ]; then
    echo "📁 项目目录不存在，开始克隆项目..."
    mkdir -p /home/<USER>/apps
    cd /home/<USER>/apps
    git clone https://github.com/peckbyte/free_lung_function_project_admin.git
fi

cd $PROJECT_DIR

echo "📦 安装依赖..."
yarn install

echo "🔧 生成Prisma客户端..."
npx prisma generate

echo "🗄️ 同步数据库..."
npx prisma db push

echo "🏗️ 构建应用..."
yarn build

echo "📝 配置PM2..."
pm2 delete lung-function-admin 2>/dev/null || true
pm2 start ecosystem.config.js

echo "💾 保存PM2配置..."
pm2 save

echo "✅ 部署完成！"
echo "🌐 应用地址: http://$(curl -s ifconfig.me):3000"
echo "📊 查看状态: pm2 status"
echo "📋 查看日志: pm2 logs lung-function-admin"

EOF

chmod +x /home/<USER>/deploy-traditional.sh
```

### 使用部署脚本

```bash
# 执行一键部署
/home/<USER>/deploy-traditional.sh
```

## 部署方式对比

| 特性 | 传统部署 | Docker部署 |
|------|----------|------------|
| **性能** | ⭐⭐⭐⭐⭐ 原生性能 | ⭐⭐⭐⭐ 轻微开销 |
| **资源占用** | ⭐⭐⭐⭐⭐ 最少 | ⭐⭐⭐ 中等 |
| **部署复杂度** | ⭐⭐⭐ 中等 | ⭐⭐⭐⭐⭐ 简单 |
| **调试便利性** | ⭐⭐⭐⭐⭐ 直接 | ⭐⭐⭐ 需要进入容器 |
| **环境隔离** | ⭐⭐ 依赖系统 | ⭐⭐⭐⭐⭐ 完全隔离 |
| **扩展性** | ⭐⭐⭐ 手动配置 | ⭐⭐⭐⭐ 容器编排 |
| **维护成本** | ⭐⭐⭐ 需要系统知识 | ⭐⭐⭐⭐ 相对简单 |

### 推荐选择

- **生产环境 + 高性能要求**: 选择传统部署
- **快速部署 + 测试环境**: 选择Docker部署
- **团队技术栈熟悉度**: 根据团队经验选择

## 故障排除联系信息

### 传统部署问题排查顺序

1. 检查PM2进程状态：`pm2 status`
2. 查看应用日志：`pm2 logs lung-function-admin`
3. 检查Nginx状态：`sudo systemctl status nginx`
4. 验证数据库连接：`npx prisma db pull`
5. 检查系统资源：`htop`

### Docker部署问题排查顺序

1. 检查Docker服务状态：`sudo systemctl status docker`
2. 查看应用日志：`docker-compose logs -f app`
3. 验证环境变量：`cat .env`
4. 检查数据库连接：`docker exec -it app_container npx prisma db push`
5. 查看系统资源：`htop` 或 `docker stats`

## 安全建议

### 通用安全配置

1. **系统安全**
   - 定期更新系统包
   - 配置防火墙规则
   - 禁用不必要的服务
   - 使用非root用户运行应用

2. **应用安全**
   - 修改默认密钥和密码
   - 启用HTTPS（生产环境必须）
   - 配置安全响应头
   - 定期更新依赖包

3. **数据安全**
   - 使用腾讯云内网连接数据库
   - 配置数据库访问白名单
   - 定期备份数据
   - 启用数据库审计日志

**重要提醒**：

- 首次部署后请立即修改 `.env` 文件中的所有默认密钥
- 定期更新系统和应用依赖
- 配置定期数据备份策略
- 监控应用运行状态和资源使用情况
- 生产环境强烈建议启用HTTPS
