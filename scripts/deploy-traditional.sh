#!/bin/bash

# 肺功能数据管理平台 - 传统部署脚本
# 适用于腾讯云CVM Ubuntu 20.04+

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_user() {
    if [ "$EUID" -eq 0 ]; then
        log_error "请不要使用root用户运行此脚本"
        log_info "建议创建普通用户: sudo useradd -m -s /bin/bash lungapp"
        exit 1
    fi
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_warning "此脚本主要针对Ubuntu系统，其他系统可能需要调整"
    fi
    
    # 检查内存
    total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [ "$total_mem" -lt 3800 ]; then
        log_warning "系统内存少于4GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    available_space=$(df / | awk 'NR==2{print $4}')
    if [ "$available_space" -lt 20971520 ]; then  # 20GB in KB
        log_warning "可用磁盘空间少于20GB，可能不足"
    fi
    
    log_success "系统检查完成"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新包列表
    sudo apt update
    
    # 安装基础工具
    sudo apt install -y curl wget git build-essential
    
    # 安装Node.js 18
    if ! command -v node &> /dev/null || [ "$(node -v | cut -d'v' -f2 | cut -d'.' -f1)" -lt 18 ]; then
        log_info "安装Node.js 18..."
        curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
    
    # 安装Yarn
    if ! command -v yarn &> /dev/null; then
        log_info "安装Yarn..."
        sudo npm install -g yarn
    fi
    
    # 安装PM2
    if ! command -v pm2 &> /dev/null; then
        log_info "安装PM2..."
        sudo npm install -g pm2
    fi
    
    # 安装Nginx
    if ! command -v nginx &> /dev/null; then
        log_info "安装Nginx..."
        sudo apt install -y nginx
        sudo systemctl enable nginx
        sudo systemctl start nginx
    fi
    
    log_success "系统依赖安装完成"
}

# 部署应用
deploy_application() {
    log_info "开始部署应用..."

    # 设置项目目录
    PROJECT_DIR="$HOME/www/wwwroot/free_lung_function_project"

    # 创建应用目录
    mkdir -p "$HOME/www/wwwroot"

    # 克隆或更新项目
    if [ ! -d "$PROJECT_DIR" ]; then
        log_info "克隆项目代码..."
        cd "$HOME/www/wwwroot"
        git clone https://github.com/peckbyte/free_lung_function_project_admin.git free_lung_function_project
    else
        log_info "更新项目代码..."
        cd "$PROJECT_DIR"
        git pull origin main
    fi
    
    cd "$PROJECT_DIR"
    
    # 安装依赖
    log_info "安装项目依赖..."
    yarn install
    
    # 配置环境变量
    if [ ! -f ".env.production" ]; then
        log_info "配置环境变量..."
        cp .env.example .env.production
        log_warning "请编辑 .env.production 文件配置数据库连接等信息"
        log_info "配置文件位置: $PROJECT_DIR/.env.production"

        # 设置默认端口为3011
        echo "PORT=3011" >> .env.production
    fi
    
    # 创建必要目录
    mkdir -p logs public/uploads
    chmod 755 logs public/uploads
    
    # 生成Prisma客户端
    log_info "生成Prisma客户端..."
    npx prisma generate
    
    # 构建应用
    log_info "构建生产版本..."
    yarn build
    
    log_success "应用部署完成"
}

# 配置PM2
configure_pm2() {
    log_info "配置PM2进程管理..."

    cd "$HOME/www/wwwroot/free_lung_function_project"

    # 检查是否已有配置文件
    if [ ! -f "ecosystem.config.js" ]; then
        log_info "创建PM2配置文件..."
        # 使用项目中的配置文件模板
        if [ -f "ecosystem.config.js.template" ]; then
            cp ecosystem.config.js.template ecosystem.config.js
        else
            # 创建基础配置文件
            cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'lung-function-admin',
    script: 'yarn',
    args: 'start',
    cwd: process.cwd(),

    // 使用 node_args 加载环境文件
    node_args: '--env-file=.env.production',

    instances: 1,
    exec_mode: 'fork',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    min_uptime: '10s',
    max_restarts: 10,

    env_production: {
      NODE_ENV: 'production',
      PORT: 3011
    },

    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    log_type: 'json',

    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000,

    health_check_url: 'http://localhost:3011/api/health',
    health_check_grace_period: 3000,

    ignore_watch: [
      'node_modules',
      'logs',
      '.next',
      '.git',
      'public/uploads'
    ]
  }]
}
EOF
        fi
    else
        log_info "使用现有的PM2配置文件"
    fi

    # 停止现有进程
    pm2 delete lung-function-admin 2>/dev/null || true

    # 启动应用
    pm2 start ecosystem.config.js --env production

    # 保存PM2配置
    pm2 save

    # 设置开机自启
    pm2 startup | grep "sudo" | bash || log_warning "请手动执行PM2 startup命令"

    log_success "PM2配置完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx反向代理..."
    
    # 获取当前用户
    current_user=$(whoami)
    project_path="$HOME/apps/free_lung_function_project_admin"
    
    # 创建Nginx配置
    sudo tee /etc/nginx/sites-available/lung-function-admin > /dev/null << EOF
server {
    listen 80;
    server_name _;
    
    # 安全配置
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件
    location /_next/static/ {
        alias $project_path/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /uploads/ {
        alias $project_path/public/uploads/;
        expires 30d;
        add_header Cache-Control "public";
    }
    
    # 主应用代理
    location / {
        proxy_pass http://127.0.0.1:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 文件上传大小限制
    client_max_body_size 10M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/lung-function-admin /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    sudo nginx -t
    
    # 重启Nginx
    sudo systemctl reload nginx
    
    log_success "Nginx配置完成"
}

# 主函数
main() {
    log_info "开始肺功能数据管理平台传统部署..."
    
    check_user
    check_system
    install_dependencies
    deploy_application
    configure_pm2
    configure_nginx
    
    log_success "部署完成！"
    echo
    log_info "访问信息:"
    echo "  - 应用地址: http://$(curl -s ifconfig.me 2>/dev/null || echo 'YOUR_SERVER_IP'):3011"
    echo "  - 本地地址: http://localhost:3011"
    echo
    log_info "管理命令:"
    echo "  - 查看状态: pm2 status"
    echo "  - 查看日志: pm2 logs lung-function-admin"
    echo "  - 重启应用: pm2 restart lung-function-admin"
    echo
    log_warning "重要提醒:"
    echo "  1. 请编辑 $HOME/www/wwwroot/free_lung_function_project/.env.production 配置数据库连接"
    echo "  2. 运行数据库迁移: cd $HOME/www/wwwroot/free_lung_function_project && npx prisma db push"
    echo "  3. 创建管理员用户: cd $HOME/www/wwwroot/free_lung_function_project && node reset-password.js"
    echo "  4. 配置防火墙: sudo ufw allow 80 && sudo ufw allow 443 && sudo ufw allow 3011"
}

# 执行主函数
main "$@"
