#!/bin/bash

# PM2 管理脚本 - 肺功能数据管理平台
# 使用方法: ./scripts/pm2-manager.sh [command] [environment]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 应用名称
APP_NAME="lung-function-admin"
CONFIG_FILE="ecosystem.config.js"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "PM2 管理脚本 - 肺功能数据管理平台"
    echo
    echo "使用方法: $0 [command] [environment]"
    echo
    echo "命令:"
    echo "  start [env]     启动应用 (env: development|production|production-cluster)"
    echo "  stop            停止应用"
    echo "  restart [env]   重启应用"
    echo "  reload [env]    重载应用 (零停机)"
    echo "  delete          删除应用"
    echo "  status          查看应用状态"
    echo "  logs            查看实时日志"
    echo "  logs-error      查看错误日志"
    echo "  logs-out        查看输出日志"
    echo "  monit           打开监控界面"
    echo "  info            查看应用详细信息"
    echo "  list            列出所有应用"
    echo "  flush           清空日志"
    echo "  save            保存当前进程列表"
    echo "  resurrect       恢复保存的进程列表"
    echo "  startup         配置开机自启"
    echo "  unstartup       取消开机自启"
    echo "  update          更新PM2"
    echo "  deploy [env]    部署到远程服务器"
    echo "  health          健康检查"
    echo "  backup          备份配置和日志"
    echo "  help            显示此帮助信息"
    echo
    echo "环境选项:"
    echo "  development           开发环境"
    echo "  production           生产环境 (单实例)"
    echo "  production-cluster   生产环境 (集群模式)"
    echo
    echo "示例:"
    echo "  $0 start production              # 启动生产环境"
    echo "  $0 restart production-cluster   # 重启集群模式"
    echo "  $0 logs                          # 查看日志"
    echo "  $0 deploy production             # 部署到生产环境"
}

# 检查PM2是否安装
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 未安装，请先安装: npm install -g pm2"
        exit 1
    fi
}

# 检查配置文件
check_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件 $CONFIG_FILE 不存在"
        exit 1
    fi
}

# 启动应用
start_app() {
    local env=${1:-production}
    log_info "启动应用 (环境: $env)..."
    
    # 确保日志目录存在
    mkdir -p logs
    
    pm2 start $CONFIG_FILE --env $env
    log_success "应用启动成功"
    pm2 status
}

# 停止应用
stop_app() {
    log_info "停止应用..."
    pm2 stop $APP_NAME 2>/dev/null || log_warning "应用可能已经停止"
    log_success "应用已停止"
}

# 重启应用
restart_app() {
    local env=${1:-production}
    log_info "重启应用 (环境: $env)..."
    pm2 restart $APP_NAME --env $env
    log_success "应用重启成功"
    pm2 status
}

# 重载应用 (零停机)
reload_app() {
    local env=${1:-production}
    log_info "重载应用 (环境: $env)..."
    pm2 reload $APP_NAME --env $env
    log_success "应用重载成功"
    pm2 status
}

# 删除应用
delete_app() {
    log_warning "确认删除应用? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        pm2 delete $APP_NAME 2>/dev/null || log_warning "应用可能已经删除"
        log_success "应用已删除"
    else
        log_info "操作已取消"
    fi
}

# 查看状态
show_status() {
    pm2 status
}

# 查看日志
show_logs() {
    pm2 logs $APP_NAME --lines 50
}

# 查看错误日志
show_error_logs() {
    pm2 logs $APP_NAME --err --lines 50
}

# 查看输出日志
show_out_logs() {
    pm2 logs $APP_NAME --out --lines 50
}

# 监控界面
show_monit() {
    pm2 monit
}

# 应用信息
show_info() {
    pm2 show $APP_NAME
}

# 列出所有应用
list_apps() {
    pm2 list
}

# 清空日志
flush_logs() {
    pm2 flush $APP_NAME
    log_success "日志已清空"
}

# 保存进程列表
save_processes() {
    pm2 save
    log_success "进程列表已保存"
}

# 恢复进程列表
resurrect_processes() {
    pm2 resurrect
    log_success "进程列表已恢复"
}

# 配置开机自启
setup_startup() {
    log_info "配置开机自启..."
    pm2 startup
    log_warning "请执行上面显示的命令来完成配置"
}

# 取消开机自启
unstartup() {
    pm2 unstartup systemd
    log_success "开机自启已取消"
}

# 更新PM2
update_pm2() {
    log_info "更新PM2..."
    pm2 update
    log_success "PM2已更新"
}

# 部署到远程服务器
deploy_app() {
    local env=${1:-production}
    log_info "部署到远程服务器 (环境: $env)..."
    pm2 deploy $CONFIG_FILE $env
    log_success "部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查应用是否运行
    if pm2 list | grep -q "$APP_NAME.*online"; then
        log_success "应用正在运行"
        
        # 检查健康检查端点
        if curl -f http://localhost:3000/api/health &>/dev/null; then
            log_success "健康检查端点响应正常"
        else
            log_warning "健康检查端点无响应"
        fi
        
        # 显示内存使用情况
        pm2 show $APP_NAME | grep -E "(memory|cpu)"
    else
        log_error "应用未运行"
        return 1
    fi
}

# 备份配置和日志
backup_data() {
    local backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
    log_info "创建备份到 $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # 备份配置文件
    cp $CONFIG_FILE "$backup_dir/"
    cp .env.production "$backup_dir/" 2>/dev/null || true
    
    # 备份日志
    cp -r logs "$backup_dir/" 2>/dev/null || true
    
    # 备份PM2配置
    pm2 save
    cp ~/.pm2/dump.pm2 "$backup_dir/" 2>/dev/null || true
    
    log_success "备份完成: $backup_dir"
}

# 主函数
main() {
    check_pm2
    check_config
    
    case "${1:-help}" in
        start)
            start_app "$2"
            ;;
        stop)
            stop_app
            ;;
        restart)
            restart_app "$2"
            ;;
        reload)
            reload_app "$2"
            ;;
        delete)
            delete_app
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        logs-error)
            show_error_logs
            ;;
        logs-out)
            show_out_logs
            ;;
        monit)
            show_monit
            ;;
        info)
            show_info
            ;;
        list)
            list_apps
            ;;
        flush)
            flush_logs
            ;;
        save)
            save_processes
            ;;
        resurrect)
            resurrect_processes
            ;;
        startup)
            setup_startup
            ;;
        unstartup)
            unstartup
            ;;
        update)
            update_pm2
            ;;
        deploy)
            deploy_app "$2"
            ;;
        health)
            health_check
            ;;
        backup)
            backup_data
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
